import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AuthService } from '../auth/auth.service';
import { ExpenseTrackerService } from '../../services/expense-tracker.service';
import { FinancialSavingsDataService } from '../financial-savings/financial-savings-data.service';
import {
  LifetimeExpensesData,
  CurrentMonthData,
  ExpensesMonthData,
  SavingPlanData,
  CardExpensesState,
  MonthlyExpense,
  DailyExpense,
  SavingPlanItem
} from '../../interfaces/dashboard';

@Injectable({
  providedIn: 'root'
})
export class CardAuthDataService {
  private authService = inject(AuthService);
  private expenseTrackerService = inject(ExpenseTrackerService);
  private financialSavingsDataService = inject(FinancialSavingsDataService);

  // Color palette for saving plans - Each plan gets a unique color
  private readonly savingPlanColors = [
    '#6B48FF', // Primary purple
    '#10B981', // Emerald green
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#3B82F6', // Blue
    '#8B5CF6', // Violet
    '#F97316', // Orange
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#EC4899', // Pink
    '#6366F1', // Indigo
    '#14B8A6', // Teal
    '#F472B6', // Rose
    '#A855F7', // Purple
    '#22C55E'  // Green
  ];

  // Helper method to properly parse transaction amounts
  private parseTransactionAmount(amountStr: string): number {
    if (!amountStr) return 0;

    // Convert to string
    const originalAmount = amountStr.toString();

    // Handle different formats:
    // "27.00 TND TND" -> 27.00
    // "27.00 TND" -> 27.00
    // "27.00" -> 27.00
    // "27,00 TND" -> 27.00 (European format)

    let cleanAmount = originalAmount;

    // Remove currency symbols and extra text
    cleanAmount = cleanAmount.replace(/TND/gi, ''); // Remove TND (case insensitive)
    cleanAmount = cleanAmount.replace(/[€$£¥]/g, ''); // Remove currency symbols
    cleanAmount = cleanAmount.replace(/[^\d.,-]/g, ''); // Keep only digits, dots, commas, minus

    // Handle European decimal format (comma as decimal separator)
    if (cleanAmount.includes(',') && !cleanAmount.includes('.')) {
      cleanAmount = cleanAmount.replace(',', '.');
    }

    // Extract the first valid number
    const numberMatch = cleanAmount.match(/^-?\d+\.?\d*/);
    const result = numberMatch ? parseFloat(numberMatch[0]) : 0;

    return result || 0;
  }

  // Helper method to properly parse transaction dates
  private parseTransactionDate(dateStr: string): Date {
    if (!dateStr) return new Date();

    // Handle different date formats:
    // "09/06/2025" (DD/MM/YYYY)
    // "2025-06-09" (YYYY-MM-DD)
    // "06/09/2025" (MM/DD/YYYY)

    let parsedDate: Date;

    if (dateStr.includes('/')) {
      // Assume DD/MM/YYYY format for European dates
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
        const year = parseInt(parts[2], 10);
        parsedDate = new Date(year, month, day);
      } else {
        parsedDate = new Date(dateStr);
      }
    } else {
      parsedDate = new Date(dateStr);
    }

    return parsedDate;
  }

  // Private signals for card data state
  private _cardState = signal<CardExpensesState>({
    lifetimeExpenses: null,
    currentMonth: null,
    expensesMonth: null,
    savingPlan: null,
    isLoading: false,
    error: null
  });

  private _allTransactions = signal<any[]>([]);
  private _currentUserEmail = signal<string | null>(null);

  // Public readonly signals
  readonly cardState = this._cardState.asReadonly();
  readonly lifetimeExpenses = computed(() => this._cardState().lifetimeExpenses);
  readonly currentMonth = computed(() => this._cardState().currentMonth);
  readonly expensesMonth = computed(() => this._cardState().expensesMonth);
  readonly savingPlan = computed(() => this._cardState().savingPlan);
  readonly isLoading = computed(() => this._cardState().isLoading);
  readonly error = computed(() => this._cardState().error);
  readonly currentUserEmail = this._currentUserEmail.asReadonly();

  // Computed auth signals for the four card components
  readonly cardLifetimeExpenses = computed((): LifetimeExpensesData | null => {
    const currentUser = this.authService.currentUser();
    const transactions = this._allTransactions();
    
    if (!currentUser?.email || transactions.length === 0) return null;

    const totalAmount = transactions.reduce((sum, transaction) => {
      const numericAmount = this.parseTransactionAmount(transaction.amount);
      return sum + numericAmount;
    }, 0);

    // Log lifetime expenses results
    if (transactions.length > 0) {
      console.log(`CardAuthDataService - Lifetime Expenses: ${transactions.length} transactions, total: ${totalAmount} TND`);
    }

    // Get account creation date - since AuthUser doesn't have created_at,
    // we'll use the earliest transaction date or a reasonable default
    const accountCreationDate = transactions.length > 0
      ? new Date(transactions[transactions.length - 1].date)
      : new Date();
    
    // Find first transaction date
    const firstTransactionDate = transactions.length > 0 
      ? new Date(transactions[transactions.length - 1].date)
      : null;

    return {
      userId: currentUser.email,
      totalAmount,
      transactionCount: transactions.length,
      accountCreationDate,
      firstTransactionDate,
      currency: 'TND'
    };
  });

  readonly cardCurrentMonth = computed((): CurrentMonthData | null => {
    const currentUser = this.authService.currentUser();
    const transactions = this._allTransactions();
    
    if (!currentUser?.email) return null;

    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Filter transactions for current month
    const monthlyTransactions = transactions.filter(transaction => {
      const transactionDate = this.parseTransactionDate(transaction.date);
      const isCurrentMonth = transactionDate.getMonth() === currentMonth &&
                             transactionDate.getFullYear() === currentYear;

      return isCurrentMonth;
    });

    const totalAmount = monthlyTransactions.reduce((sum, transaction) => {
      const numericAmount = this.parseTransactionAmount(transaction.amount);
      return sum + numericAmount;
    }, 0);

    // Log current month results
    if (monthlyTransactions.length > 0) {
      console.log(`CardAuthDataService - Current Month: ${monthlyTransactions.length} transactions, total: ${totalAmount} TND`);
    }

    // Create daily breakdown
    const dailyBreakdown: DailyExpense[] = [];
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    
    for (let day = 1; day <= daysInMonth; day++) {
      const dayTransactions = monthlyTransactions.filter(transaction => {
        const transactionDate = this.parseTransactionDate(transaction.date);
        return transactionDate.getDate() === day;
      });

      const dayAmount = dayTransactions.reduce((sum, transaction) => {
        const numericAmount = this.parseTransactionAmount(transaction.amount);
        return sum + numericAmount;
      }, 0);

      if (dayAmount > 0) {
        dailyBreakdown.push({
          day,
          amount: dayAmount,
          transactionCount: dayTransactions.length
        });
      }
    }

    return {
      userId: currentUser.email,
      totalAmount,
      transactionCount: monthlyTransactions.length,
      month: currentMonth,
      year: currentYear,
      currency: 'TND',
      dailyBreakdown
    };
  });

  readonly cardExpensesMonth = computed((): ExpensesMonthData | null => {
    const currentUser = this.authService.currentUser();
    const transactions = this._allTransactions();
    
    if (!currentUser?.email) return null;

    // Group transactions by month/year
    const monthlyMap = new Map<string, { amount: number; count: number; month: number; year: number }>();

    transactions.forEach(transaction => {
      const transactionDate = this.parseTransactionDate(transaction.date);
      const month = transactionDate.getMonth();
      const year = transactionDate.getFullYear();
      const key = `${year}-${month}`;

      const numericAmount = this.parseTransactionAmount(transaction.amount);

      if (monthlyMap.has(key)) {
        const existing = monthlyMap.get(key)!;
        existing.amount += numericAmount;
        existing.count += 1;
      } else {
        monthlyMap.set(key, {
          amount: numericAmount,
          count: 1,
          month,
          year
        });
      }
    });

    // Convert to array and sort by date
    const monthlyBreakdown: MonthlyExpense[] = Array.from(monthlyMap.values())
      .map(item => ({
        month: item.month,
        year: item.year,
        amount: item.amount,
        transactionCount: item.count,
        monthName: new Date(item.year, item.month).toLocaleDateString('en-US', { 
          month: 'long', 
          year: 'numeric' 
        })
      }))
      .sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });

    const totalAmount = monthlyBreakdown.reduce((sum, month) => sum + month.amount, 0);
    const averageMonthlyAmount = monthlyBreakdown.length > 0 ? totalAmount / monthlyBreakdown.length : 0;

    return {
      userId: currentUser.email,
      monthlyBreakdown,
      totalAmount,
      averageMonthlyAmount,
      currency: 'TND'
    };
  });

  readonly cardSavingPlan = computed((): SavingPlanData | null => {
    const currentUser = this.authService.currentUser();

    if (!currentUser?.email) return null;

    // Get actual saving plans from Financial Savings folder
    const financialSavingsPlans = this.financialSavingsDataService.plans();

    // Convert financial savings plans to dashboard saving plan items
    const savingPlans: SavingPlanItem[] = financialSavingsPlans.map((plan, index) => {
      // Calculate percentage completion
      const percentage = plan.objective > 0 ? Math.min((plan.current / plan.objective) * 100, 100) : 0;

      // Calculate days remaining based on period
      let daysRemaining = 0;
      if (plan.endDate) {
        const now = new Date();
        const endDate = new Date(plan.endDate);
        daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
      } else {
        // Estimate based on period
        const periodDays: { [key: string]: number } = {
          '3 months': 90,
          '6 months': 180,
          '9 months': 270,
          '1 year': 365,
          'Custom': 365
        };
        const startDate = new Date(plan.startDate);
        const now = new Date();
        const elapsedDays = Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        const totalPeriodDays = periodDays[plan.period] || 365;
        daysRemaining = Math.max(0, totalPeriodDays - elapsedDays);
      }

      return {
        id: plan.id,
        name: plan.name,
        target: plan.objective,
        current: plan.current,
        percentage,
        color: this.savingPlanColors[index % this.savingPlanColors.length],
        category: 'savings', // All are savings category
        daysRemaining,
        isActive: true
      };
    });

    const totalTarget = savingPlans.reduce((sum, plan) => sum + plan.target, 0);
    const totalCurrent = savingPlans.reduce((sum, plan) => sum + plan.current, 0);
    const totalPercentage = totalTarget > 0 ? (totalCurrent / totalTarget) * 100 : 0;

    return {
      userId: currentUser.email,
      plans: savingPlans,
      totalTarget,
      totalCurrent,
      totalPercentage,
      currency: 'TND'
    };
  });

  constructor() {
    // Watch for auth user changes and update current user email
    effect(() => {
      const authUser = this.authService.currentUser();
      console.log('CardAuthDataService effect triggered - authUser:', authUser);
      if (authUser) {
        this._currentUserEmail.set(authUser.email);
        this.loadUserTransactions(authUser.email);
      } else {
        this._currentUserEmail.set(null);
        this._allTransactions.set([]);
        this.clearCardData();
      }
    }, { allowSignalWrites: true });

    // Watch for localStorage changes to automatically update when new transactions are added
    if (typeof window !== 'undefined') {
      window.addEventListener('storage', (event) => {
        const userEmail = this._currentUserEmail();
        if (userEmail && event.key === `receeto-transactions_${userEmail}`) {
          console.log('CardAuthDataService: localStorage changed, reloading transactions');
          this.loadUserTransactions(userEmail);
        }
      });
    }
  }

  // Load user-specific transactions from localStorage
  private loadUserTransactions(userEmail: string): void {
    try {
      const userTransactionKey = `receeto-transactions_${userEmail}`;
      const savedTransactions = localStorage.getItem(userTransactionKey);

      if (savedTransactions) {
        const parsedTransactions = JSON.parse(savedTransactions);
        this._allTransactions.set(parsedTransactions);
        console.log(`CardAuthDataService: Loaded ${parsedTransactions.length} transactions for user: ${userEmail}`);
      } else {
        this._allTransactions.set([]);
        console.log(`CardAuthDataService: No transactions found for user: ${userEmail}`);
      }
    } catch (error) {
      console.error('CardAuthDataService: Error loading transactions:', error);
      this._allTransactions.set([]);
    }
  }

  // Clear card data (on logout)
  private clearCardData(): void {
    this._cardState.set({
      lifetimeExpenses: null,
      currentMonth: null,
      expensesMonth: null,
      savingPlan: null,
      isLoading: false,
      error: null
    });
    console.log('CardAuthDataService: Card data cleared');
  }

  // Public method to refresh transaction data
  refreshTransactions(): void {
    const userEmail = this._currentUserEmail();
    if (userEmail) {
      this.loadUserTransactions(userEmail);
    }
  }

  // Method to be called when new transactions are added to trigger reactive updates
  notifyTransactionAdded(): void {
    const userEmail = this._currentUserEmail();
    if (userEmail) {
      console.log('CardAuthDataService: Transaction added notification, reloading...');
      this.loadUserTransactions(userEmail);
    }
  }

  // Helper methods for easy access to computed data
  getLifetimeExpensesTotal(): number {
    return this.cardLifetimeExpenses()?.totalAmount || 0;
  }

  getCurrentMonthTotal(): number {
    return this.cardCurrentMonth()?.totalAmount || 0;
  }

  getMonthlyExpensesData(): MonthlyExpense[] {
    return this.cardExpensesMonth()?.monthlyBreakdown || [];
  }

  getSavingPlanItems(): SavingPlanItem[] {
    return this.cardSavingPlan()?.plans || [];
  }

  getTotalSavingsPercentage(): number {
    return Math.round(this.cardSavingPlan()?.totalPercentage || 0);
  }
}
